<template>
  <div class="file-viewer">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-2">
        <div>
          <h1 class="text-2xl font-bold">{{ title }}</h1>
          <p v-if="description" class="text-muted-foreground mt-1">{{ description }}</p>
        </div>
        <div class="flex items-center gap-2">
          <Button variant="outline" size="sm" @click="copyToClipboard" class="gap-2">
            <Copy class="h-4 w-4" />
            Copy
          </Button>
          <Button variant="outline" size="sm" @click="downloadFile" class="gap-2">
            <Download class="h-4 w-4" />
            Download
          </Button>
        </div>
      </div>

      <!-- Breadcrumb -->
      <div class="flex items-center gap-2 text-sm text-muted-foreground">
        <Home class="h-4 w-4" />
        <template v-for="(crumb, index) in breadcrumbs" :key="index">
          <ChevronRight class="h-4 w-4" />
          <NuxtLink v-if="crumb.path" :to="crumb.path" class="hover:text-foreground transition-colors">
            {{ crumb.title }}
          </NuxtLink>
          <span v-else class="text-foreground">{{ crumb.title }}</span>
        </template>
      </div>
    </div>

    <!-- Content -->
    <div class="border rounded-lg overflow-hidden">
      <!-- Toolbar -->
      <div class="flex items-center justify-between px-4 py-2 bg-muted/50 border-b">
        <div class="flex items-center gap-2">
          <component :is="fileIcon" class="h-4 w-4" />
          <span class="text-sm font-medium">{{ filename }}</span>
          <span class="text-xs text-muted-foreground">{{ fileInfo.displayName }}</span>
        </div>
        <div class="flex items-center gap-2">
          <Button v-if="fileInfo.supportsLineNumbers && (fileInfo.type === 'code' || fileInfo.type === 'yaml')"
            variant="ghost" size="sm" @click="toggleLineNumbers" class="text-xs">
            {{ showLineNumbers ? 'Hide' : 'Show' }} Lines
          </Button>
          <Button v-if="fileInfo.supportsWordWrap" variant="ghost" size="sm" @click="toggleWordWrap" class="text-xs">
            {{ wordWrap ? 'No Wrap' : 'Wrap' }}
          </Button>
        </div>
      </div>

      <!-- Content Renderer -->
      <div class="relative">
        <!-- Markdown Content -->
        <div v-if="fileInfo.type === 'markdown'" class="p-6">
          <MarkdownRenderer :content="content" />
        </div>

        <!-- Code/YAML Content -->
        <div v-else-if="fileInfo.type === 'code' || fileInfo.type === 'yaml'" class="overflow-auto max-h-[70vh]">
          <div class="flex">
            <!-- Line Numbers -->
            <div v-if="showLineNumbers"
              class="flex-shrink-0 p-4 pr-2 text-xs text-muted-foreground select-none border-r bg-gray-50 dark:bg-gray-800">
              <div v-for="(line, index) in lines" :key="index" class="leading-relaxed">
                {{ index + 1 }}
              </div>
            </div>

            <!-- Code Content -->
            <div class="flex-1">
              <pre class="p-4 text-xs leading-relaxed" :class="{
                'whitespace-pre-wrap': wordWrap,
                'whitespace-pre': !wordWrap
              }"><code v-html="highlightedCode"></code></pre>
            </div>
          </div>
        </div>

        <!-- Plain Text Content -->
        <div v-else class="overflow-auto max-h-[70vh]">
          <div class="flex">
            <!-- Line Numbers -->
            <div v-if="showLineNumbers"
              class="flex-shrink-0 p-4 pr-2 text-xs text-muted-foreground select-none border-r bg-gray-50 dark:bg-gray-800">
              <div v-for="(line, index) in lines" :key="index" class="leading-relaxed">
                {{ index + 1 }}
              </div>
            </div>

            <!-- Text Content -->
            <div class="flex-1">
              <pre class="p-4 text-xs leading-relaxed font-mono" :class="{
                'whitespace-pre-wrap': wordWrap,
                'whitespace-pre': !wordWrap
              }">{{ content }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Metadata -->
    <div v-if="metadata" class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="p-4 border rounded-lg">
        <h3 class="font-medium mb-2">File Info</h3>
        <div class="space-y-1 text-sm text-muted-foreground">
          <div>Size: {{ formatFileSize(metadata.size || content.length) }}</div>
          <div>Lines: {{ lines.length }}</div>
          <div>Type: {{ fileInfo.displayName }}</div>
        </div>
      </div>

      <div v-if="metadata.lastModified" class="p-4 border rounded-lg">
        <h3 class="font-medium mb-2">Last Modified</h3>
        <div class="text-sm text-muted-foreground">
          {{ formatDate(metadata.lastModified) }}
        </div>
      </div>

      <div v-if="metadata.tags && metadata.tags.length > 0" class="p-4 border rounded-lg">
        <h3 class="font-medium mb-2">Tags</h3>
        <div class="flex flex-wrap gap-1">
          <span v-for="tag in metadata.tags" :key="tag"
            class="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded">
            {{ tag }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Copy, Download, Home, ChevronRight, FileText, Code, Braces, Terminal, Database } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import { toast } from 'vue-sonner'
import MarkdownRenderer from './MarkdownRenderer.vue'
import { highlightYaml, escapeHtmlServer } from '~/lib/syntaxHighlighting'

interface FileInfo {
  type: 'yaml' | 'markdown' | 'code' | 'text'
  language?: string
  displayName: string
  mimeType: string
  extension: string
  basename: string
  supportsLineNumbers: boolean
  supportsWordWrap: boolean
  defaultHighlighting: boolean
}

interface Props {
  content: string
  title: string
  description?: string
  filename: string
  fileInfo: FileInfo
  breadcrumbs?: Array<{ title: string; path?: string }>
  metadata?: {
    size?: number
    lastModified?: string
    created?: string
    tags?: string[]
    [key: string]: any
  }
}

const props = defineProps<Props>()

const codeContainer = ref<HTMLElement>()
const showLineNumbers = ref(props.fileInfo.supportsLineNumbers)
const wordWrap = ref(false)

// Split content into lines
const lines = computed(() => props.content.split('\n'))

// Get appropriate icon for file type
const fileIcon = computed(() => {
  switch (props.fileInfo.type) {
    case 'code':
      if (props.fileInfo.language === 'json') return Braces
      if (props.fileInfo.language === 'bash' || props.fileInfo.language === 'shell') return Terminal
      if (props.fileInfo.language === 'sql') return Database
      return Code
    case 'yaml':
    case 'markdown':
    case 'text':
    default:
      return FileText
  }
})

// Syntax highlighting
const highlightedCode = computed(() => {
  if (!props.content) return ''

  // Use YAML highlighting for YAML files
  if (props.fileInfo.type === 'yaml') {
    return highlightYaml(props.content)
  }

  // For other code files, return escaped HTML for now
  // TODO: Implement Shiki highlighting on client side
  if (props.fileInfo.type === 'code') {
    return escapeHtmlServer(props.content)
  }

  return escapeHtmlServer(props.content)
})

const toggleLineNumbers = () => {
  showLineNumbers.value = !showLineNumbers.value
}

const toggleWordWrap = () => {
  wordWrap.value = !wordWrap.value
}

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(props.content)
    toast.success('Content copied to clipboard')
  } catch (error) {
    toast.error('Failed to copy to clipboard')
  }
}

const downloadFile = () => {
  const blob = new Blob([props.content], { type: props.fileInfo.mimeType })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = props.filename
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.file-viewer {
  width: 100%;
}

/* Word wrap styles */
pre.whitespace-pre-wrap {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

pre.whitespace-pre {
  white-space: pre;
  overflow-x: auto;
}

/* Custom scrollbar for code container */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

.dark .overflow-auto::-webkit-scrollbar-track {
  background-color: #1f2937;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 4px;
}

.dark .overflow-auto::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

.dark .overflow-auto::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

/* Syntax highlighting styles */
:deep(.text-blue-600) {
  @apply text-blue-600 dark:text-blue-400;
}

:deep(.text-green-600) {
  @apply text-green-600 dark:text-green-400;
}

:deep(.text-gray-500) {
  @apply text-gray-500 dark:text-gray-400;
}

:deep(.text-orange-600) {
  @apply text-orange-600 dark:text-orange-400;
}
</style>
