<template>
  <div class="markdown-content">
    <div v-if="isLoading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
    </div>
    <div v-else-if="error" class="p-4 border border-destructive/20 rounded-lg bg-destructive/5">
      <p class="text-destructive">Failed to render Markdown: {{ error }}</p>
    </div>
    <div v-else v-html="renderedHtml" class="prose prose-sm max-w-none dark:prose-invert"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { unified } from 'unified'
import remarkParse from 'remark-parse'
import remarkRehype from 'remark-rehype'
import rehypeRaw from 'rehype-raw'
import rehypeStringify from 'rehype-stringify'

interface Props {
  content: string
  baseUrl?: string
}

const props = defineProps<Props>()

const renderedHtml = ref('')
const isLoading = ref(true)
const error = ref<string | null>(null)

// Markdown processor
const processor = unified()
  .use(remarkParse)
  .use(remarkRehype, { allowDangerousHtml: true })
  .use(rehypeRaw)
  .use(rehypeStringify)

/**
 * Process markdown content to HTML
 */
async function processMarkdown(content: string): Promise<string> {
  try {
    const result = await processor.process(content)
    return String(result)
  } catch (err) {
    console.error('Markdown processing error:', err)
    throw new Error('Failed to process Markdown content')
  }
}

/**
 * Render the markdown content
 */
async function renderMarkdown() {
  if (!props.content) {
    renderedHtml.value = ''
    isLoading.value = false
    return
  }

  isLoading.value = true
  error.value = null

  try {
    const html = await processMarkdown(props.content)
    renderedHtml.value = html
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Unknown error'
    renderedHtml.value = ''
  } finally {
    isLoading.value = false
  }
}

// Watch for content changes
watch(() => props.content, renderMarkdown, { immediate: true })

onMounted(() => {
  renderMarkdown()
})
</script>

<style scoped>
.markdown-content {
  width: 100%;
}

/* Custom prose styles to match the existing design */
.markdown-content :deep(.prose) {
  color: inherit;
  max-width: none;
}

.markdown-content :deep(.prose h1) {
  @apply text-2xl font-bold mb-4 mt-6 first:mt-0;
}

.markdown-content :deep(.prose h2) {
  @apply text-xl font-semibold mb-3 mt-5;
}

.markdown-content :deep(.prose h3) {
  @apply text-lg font-medium mb-2 mt-4;
}

.markdown-content :deep(.prose h4) {
  @apply text-base font-medium mb-2 mt-3;
}

.markdown-content :deep(.prose h5) {
  @apply text-sm font-medium mb-1 mt-2;
}

.markdown-content :deep(.prose h6) {
  @apply text-sm font-medium mb-1 mt-2;
}

.markdown-content :deep(.prose p) {
  @apply mb-4 leading-relaxed;
}

.markdown-content :deep(.prose ul) {
  @apply mb-4 pl-6;
}

.markdown-content :deep(.prose ol) {
  @apply mb-4 pl-6;
}

.markdown-content :deep(.prose li) {
  @apply mb-1;
}

.markdown-content :deep(.prose blockquote) {
  @apply border-l-4 border-muted-foreground/20 pl-4 italic my-4;
}

.markdown-content :deep(.prose code) {
  @apply bg-muted px-1.5 py-0.5 rounded text-sm font-mono;
}

.markdown-content :deep(.prose pre) {
  @apply bg-muted p-4 rounded-lg overflow-x-auto my-4;
}

.markdown-content :deep(.prose pre code) {
  @apply bg-transparent p-0;
}

.markdown-content :deep(.prose table) {
  @apply w-full border-collapse border border-border my-4;
}

.markdown-content :deep(.prose th) {
  @apply border border-border px-3 py-2 bg-muted font-medium text-left;
}

.markdown-content :deep(.prose td) {
  @apply border border-border px-3 py-2;
}

.markdown-content :deep(.prose a) {
  @apply text-primary hover:underline;
}

.markdown-content :deep(.prose img) {
  @apply max-w-full h-auto rounded-lg my-4;
}

.markdown-content :deep(.prose hr) {
  @apply border-t border-border my-6;
}

/* Dark mode adjustments */
.dark .markdown-content :deep(.prose code) {
  @apply bg-gray-800;
}

.dark .markdown-content :deep(.prose pre) {
  @apply bg-gray-800;
}

.dark .markdown-content :deep(.prose th) {
  @apply bg-gray-800;
}

.dark .markdown-content :deep(.prose blockquote) {
  @apply border-l-gray-600;
}
</style>
