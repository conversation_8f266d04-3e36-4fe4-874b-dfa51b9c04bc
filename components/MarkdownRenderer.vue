<template>
  <div class="markdown-content prose prose-sm max-w-none dark:prose-invert">
    <div v-html="renderedHtml"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  content: string
}

const props = defineProps<Props>()

/**
 * Simple Markdown to HTML converter using basic regex patterns
 * This provides basic formatting while keeping it simple and fast
 */
const renderedHtml = computed(() => {
  if (!props.content) return ''

  let html = props.content

  // Escape HTML first to prevent XSS
  html = html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')

  // Convert line breaks to proper HTML
  html = html.replace(/\n/g, '<br>')

  // Headers (must come before other formatting)
  html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>')
  html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>')
  html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>')

  // Bold and italic
  html = html.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>')
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')

  // Code blocks (triple backticks)
  html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')

  // Inline code
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>')

  // Links
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')

  // Lists (simple implementation)
  html = html.replace(/^[\s]*[-*+] (.+$)/gm, '<li>$1</li>')
  html = html.replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')

  // Numbered lists
  html = html.replace(/^[\s]*\d+\. (.+$)/gm, '<li>$1</li>')
  html = html.replace(/(<li>.*<\/li>)/s, '<ol>$1</ol>')

  // Blockquotes
  html = html.replace(/^> (.+$)/gm, '<blockquote>$1</blockquote>')

  // Horizontal rules
  html = html.replace(/^---$/gm, '<hr>')

  // Clean up multiple consecutive <br> tags and replace with proper paragraphs
  html = html.replace(/(<br>){2,}/g, '</p><p>')
  html = `<p>${html}</p>`

  // Clean up empty paragraphs
  html = html.replace(/<p><\/p>/g, '')
  html = html.replace(/<p>(<h[1-6]>)/g, '$1')
  html = html.replace(/(<\/h[1-6]>)<\/p>/g, '$1')
  html = html.replace(/<p>(<hr>)<\/p>/g, '$1')
  html = html.replace(/<p>(<blockquote>)/g, '$1')
  html = html.replace(/(<\/blockquote>)<\/p>/g, '$1')
  html = html.replace(/<p>(<ul>)/g, '$1')
  html = html.replace(/(<\/ul>)<\/p>/g, '$1')
  html = html.replace(/<p>(<ol>)/g, '$1')
  html = html.replace(/(<\/ol>)<\/p>/g, '$1')
  html = html.replace(/<p>(<pre>)/g, '$1')
  html = html.replace(/(<\/pre>)<\/p>/g, '$1')

  return html
})
</script>

<style scoped>
.markdown-content {
  width: 100%;
}

/* Custom prose styles to match the existing design */
.markdown-content :deep(.prose) {
  color: inherit;
  max-width: none;
}

.markdown-content :deep(.prose h1) {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  margin-top: 1.5rem;
}

.markdown-content :deep(.prose h1:first-child) {
  margin-top: 0;
}

.markdown-content :deep(.prose h2) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  margin-top: 1.25rem;
}

.markdown-content :deep(.prose h3) {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}

.markdown-content :deep(.prose h4) {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  margin-top: 0.75rem;
}

.markdown-content :deep(.prose h5) {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  margin-top: 0.5rem;
}

.markdown-content :deep(.prose h6) {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  margin-top: 0.5rem;
}

.markdown-content :deep(.prose p) {
  margin-bottom: 1rem;
  line-height: 1.625;
}

.markdown-content :deep(.prose ul) {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.markdown-content :deep(.prose ol) {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.markdown-content :deep(.prose li) {
  margin-bottom: 0.25rem;
}

.markdown-content :deep(.prose blockquote) {
  border-left: 4px solid hsl(var(--muted-foreground) / 0.2);
  padding-left: 1rem;
  font-style: italic;
  margin: 1rem 0;
}

.markdown-content :deep(.prose code) {
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.markdown-content :deep(.prose pre) {
  background-color: hsl(var(--muted));
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content :deep(.prose pre code) {
  background-color: transparent;
  padding: 0;
}

.markdown-content :deep(.prose table) {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid hsl(var(--border));
  margin: 1rem 0;
}

.markdown-content :deep(.prose th) {
  border: 1px solid hsl(var(--border));
  padding: 0.75rem;
  background-color: hsl(var(--muted));
  font-weight: 500;
  text-align: left;
}

.markdown-content :deep(.prose td) {
  border: 1px solid hsl(var(--border));
  padding: 0.75rem;
}

.markdown-content :deep(.prose a) {
  color: hsl(var(--primary));
  text-decoration: none;
}

.markdown-content :deep(.prose a:hover) {
  text-decoration: underline;
}

.markdown-content :deep(.prose img) {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.markdown-content :deep(.prose hr) {
  border-top: 1px solid hsl(var(--border));
  margin: 1.5rem 0;
}

/* Dark mode adjustments */
.dark .markdown-content :deep(.prose code) {
  background-color: rgb(31 41 55);
}

.dark .markdown-content :deep(.prose pre) {
  background-color: rgb(31 41 55);
}

.dark .markdown-content :deep(.prose th) {
  background-color: rgb(31 41 55);
}

.dark .markdown-content :deep(.prose blockquote) {
  border-left-color: rgb(75 85 99);
}
</style>
