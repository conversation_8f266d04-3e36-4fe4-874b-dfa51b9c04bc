<template>
  <div class="markdown-content prose prose-sm max-w-none dark:prose-invert">
    <div v-html="renderedHtml"></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  content: string
}

const props = defineProps<Props>()

/**
 * Simple Markdown to HTML converter using basic regex patterns
 * This provides basic formatting while keeping it simple and fast
 */
const renderedHtml = computed(() => {
  if (!props.content) return ''

  let html = props.content

  // Escape HTML first to prevent XSS
  html = html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')

  // Split into lines for processing
  const lines = html.split('\n')
  const processedLines: string[] = []
  let inCodeBlock = false
  let inList = false
  let listType = ''

  for (let i = 0; i < lines.length; i++) {
    let line = lines[i]

    // Handle code blocks first
    if (line.trim().startsWith('```')) {
      if (inCodeBlock) {
        processedLines.push('</code></pre>')
        inCodeBlock = false
      } else {
        const language = line.trim().substring(3).trim()
        processedLines.push(`<pre><code${language ? ` class="language-${language}"` : ''}>`)
        inCodeBlock = true
      }
      continue
    }

    if (inCodeBlock) {
      processedLines.push(line)
      continue
    }

    // Handle headers
    if (line.match(/^#{1,6} /)) {
      const level = line.match(/^#+/)?.[0].length || 1
      const text = line.replace(/^#+\s*/, '')
      processedLines.push(`<h${level}>${text}</h${level}>`)
      continue
    }

    // Handle horizontal rules
    if (line.trim() === '---' || line.trim() === '***') {
      processedLines.push('<hr>')
      continue
    }

    // Handle blockquotes
    if (line.trim().startsWith('> ')) {
      const text = line.replace(/^>\s*/, '')
      processedLines.push(`<blockquote>${text}</blockquote>`)
      continue
    }

    // Handle lists
    const bulletMatch = line.match(/^(\s*)[-*+]\s+(.+)$/)
    const numberedMatch = line.match(/^(\s*)\d+\.\s+(.+)$/)

    if (bulletMatch || numberedMatch) {
      const isNumbered = !!numberedMatch
      const text = (bulletMatch || numberedMatch)?.[2] || ''

      if (!inList) {
        processedLines.push(isNumbered ? '<ol>' : '<ul>')
        inList = true
        listType = isNumbered ? 'ol' : 'ul'
      } else if ((isNumbered && listType === 'ul') || (!isNumbered && listType === 'ol')) {
        processedLines.push(`</${listType}>`)
        processedLines.push(isNumbered ? '<ol>' : '<ul>')
        listType = isNumbered ? 'ol' : 'ul'
      }

      processedLines.push(`<li>${text}</li>`)
      continue
    } else if (inList && line.trim() === '') {
      // Empty line in list - continue list
      continue
    } else if (inList) {
      // End of list
      processedLines.push(`</${listType}>`)
      inList = false
      listType = ''
    }

    // Handle empty lines
    if (line.trim() === '') {
      processedLines.push('')
      continue
    }

    // Regular paragraph text
    processedLines.push(line)
  }

  // Close any open lists
  if (inList) {
    processedLines.push(`</${listType}>`)
  }

  // Close any open code blocks
  if (inCodeBlock) {
    processedLines.push('</code></pre>')
  }

  // Join lines back together
  html = processedLines.join('\n')

  // Apply inline formatting
  // Bold and italic (order matters)
  html = html.replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>')
  html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
  html = html.replace(/\*(.*?)\*/g, '<em>$1</em>')

  // Inline code
  html = html.replace(/`([^`]+)`/g, '<code>$1</code>')

  // Links
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')

  // Convert line breaks to paragraphs
  const paragraphs = html.split('\n\n')
  const finalParagraphs = paragraphs.map(para => {
    const trimmed = para.trim()
    if (!trimmed) return ''

    // Don't wrap block elements in paragraphs
    if (trimmed.match(/^<(h[1-6]|hr|blockquote|ul|ol|pre|div)/)) {
      return trimmed
    }

    // Don't wrap if it's already a complete block
    if (trimmed.startsWith('<') && trimmed.endsWith('>')) {
      return trimmed
    }

    return `<p>${trimmed.replace(/\n/g, '<br>')}</p>`
  })

  return finalParagraphs.filter(p => p).join('\n\n')
})
</script>

<style scoped>
.markdown-content {
  width: 100%;
}

/* Custom prose styles to match the existing design */
.markdown-content :deep(.prose) {
  color: inherit;
  max-width: none;
}

.markdown-content :deep(.prose h1) {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  margin-top: 1.5rem;
}

.markdown-content :deep(.prose h1:first-child) {
  margin-top: 0;
}

.markdown-content :deep(.prose h2) {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  margin-top: 1.25rem;
}

.markdown-content :deep(.prose h3) {
  font-size: 1.125rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  margin-top: 1rem;
}

.markdown-content :deep(.prose h4) {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  margin-top: 0.75rem;
}

.markdown-content :deep(.prose h5) {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  margin-top: 0.5rem;
}

.markdown-content :deep(.prose h6) {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.25rem;
  margin-top: 0.5rem;
}

.markdown-content :deep(.prose p) {
  margin-bottom: 1rem;
  line-height: 1.625;
}

.markdown-content :deep(.prose ul) {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.markdown-content :deep(.prose ol) {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.markdown-content :deep(.prose li) {
  margin-bottom: 0.25rem;
}

.markdown-content :deep(.prose blockquote) {
  border-left: 4px solid hsl(var(--muted-foreground) / 0.2);
  padding-left: 1rem;
  font-style: italic;
  margin: 1rem 0;
}

.markdown-content :deep(.prose code) {
  background-color: hsl(var(--muted));
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.markdown-content :deep(.prose pre) {
  background-color: hsl(var(--muted));
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.markdown-content :deep(.prose pre code) {
  background-color: transparent;
  padding: 0;
}

.markdown-content :deep(.prose table) {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid hsl(var(--border));
  margin: 1rem 0;
}

.markdown-content :deep(.prose th) {
  border: 1px solid hsl(var(--border));
  padding: 0.75rem;
  background-color: hsl(var(--muted));
  font-weight: 500;
  text-align: left;
}

.markdown-content :deep(.prose td) {
  border: 1px solid hsl(var(--border));
  padding: 0.75rem;
}

.markdown-content :deep(.prose a) {
  color: hsl(var(--primary));
  text-decoration: none;
}

.markdown-content :deep(.prose a:hover) {
  text-decoration: underline;
}

.markdown-content :deep(.prose img) {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1rem 0;
}

.markdown-content :deep(.prose hr) {
  border-top: 1px solid hsl(var(--border));
  margin: 1.5rem 0;
}

/* Dark mode adjustments */
.dark .markdown-content :deep(.prose code) {
  background-color: rgb(31 41 55);
}

.dark .markdown-content :deep(.prose pre) {
  background-color: rgb(31 41 55);
}

.dark .markdown-content :deep(.prose th) {
  background-color: rgb(31 41 55);
}

.dark .markdown-content :deep(.prose blockquote) {
  border-left-color: rgb(75 85 99);
}
</style>
