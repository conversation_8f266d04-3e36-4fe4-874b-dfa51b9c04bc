<template>
  <div class="yaml-viewer">
    <!-- Header -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-2">
        <div>
          <h1 class="text-2xl font-bold">{{ title }}</h1>
          <p v-if="description" class="text-muted-foreground mt-1">{{ description }}</p>
        </div>
        <div class="flex items-center gap-2">
          <Button variant="outline" size="sm" @click="copyToClipboard" class="gap-2">
            <Copy class="h-4 w-4" />
            Copy
          </Button>
          <Button variant="outline" size="sm" @click="downloadFile" class="gap-2">
            <Download class="h-4 w-4" />
            Download
          </Button>
        </div>
      </div>

      <!-- Breadcrumb -->
      <div class="flex items-center gap-2 text-sm text-muted-foreground">
        <Home class="h-4 w-4" />
        <template v-for="(crumb, index) in breadcrumbs" :key="index">
          <ChevronRight class="h-4 w-4" />
          <NuxtLink v-if="crumb.path" :to="crumb.path" class="hover:text-foreground transition-colors">
            {{ crumb.title }}
          </NuxtLink>
          <span v-else class="text-foreground">{{ crumb.title }}</span>
        </template>
      </div>
    </div>

    <!-- Content -->
    <div class="border rounded-lg overflow-hidden">
      <!-- Toolbar -->
      <div class="flex items-center justify-between px-4 py-2 bg-muted/50 border-b">
        <div class="flex items-center gap-2">
          <FileText class="h-4 w-4" />
          <span class="text-sm font-medium">{{ filename }}</span>
          <span class="text-xs text-muted-foreground">YAML</span>
        </div>
        <div class="flex items-center gap-2">
          <Button variant="ghost" size="sm" @click="toggleLineNumbers" class="text-xs">
            {{ showLineNumbers ? 'Hide' : 'Show' }} Lines
          </Button>
          <Button variant="ghost" size="sm" @click="toggleWordWrap" class="text-xs">
            {{ wordWrap ? 'No Wrap' : 'Wrap' }}
          </Button>
        </div>
      </div>

      <!-- Code Content -->
      <div class="relative">
        <div ref="codeContainer" class="overflow-auto max-h-[70vh]">
          <div class="flex">
            <!-- Line Numbers -->
            <div v-if="showLineNumbers"
              class="flex-shrink-0 p-4 pr-2 text-xs text-muted-foreground select-none border-r bg-gray-50 dark:bg-gray-800">
              <div v-for="(line, index) in lines" :key="index" class="leading-relaxed">
                {{ index + 1 }}
              </div>
            </div>

            <!-- Code Content -->
            <div class="flex-1">
              <pre class="p-4 text-xs leading-relaxed" :class="{
                'whitespace-pre-wrap': wordWrap,
                'whitespace-pre': !wordWrap
              }"><code v-html="highlightedCode"></code></pre>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Metadata -->
    <div v-if="metadata" class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="p-4 border rounded-lg">
        <h3 class="font-medium mb-2">File Info</h3>
        <div class="space-y-1 text-sm text-muted-foreground">
          <div>Size: {{ formatFileSize(content.length) }}</div>
          <div>Lines: {{ lines.length }}</div>
          <div>Type: YAML</div>
        </div>
      </div>

      <div v-if="metadata.lastModified" class="p-4 border rounded-lg">
        <h3 class="font-medium mb-2">Last Modified</h3>
        <div class="text-sm text-muted-foreground">
          {{ formatDate(metadata.lastModified) }}
        </div>
      </div>

      <div v-if="metadata.tags && metadata.tags.length > 0" class="p-4 border rounded-lg">
        <h3 class="font-medium mb-2">Tags</h3>
        <div class="flex flex-wrap gap-1">
          <span v-for="tag in metadata.tags" :key="tag"
            class="px-2 py-1 text-xs bg-secondary text-secondary-foreground rounded">
            {{ tag }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Copy, Download, Home, ChevronRight, FileText } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'
import { toast } from 'vue-sonner'

interface Props {
  content: string
  title: string
  description?: string
  filename: string
  breadcrumbs?: Array<{ title: string; path?: string }>
  metadata?: {
    lastModified?: string
    tags?: string[]
    [key: string]: any
  }
}

const props = defineProps<Props>()


const codeContainer = ref<HTMLElement>()
const showLineNumbers = ref(true)
const wordWrap = ref(false)

// Split content into lines
const lines = computed(() => props.content.split('\n'))

// Simple YAML syntax highlighting (basic implementation)
const highlightedCode = computed(() => {
  if (!props.content) return ''

  // Split content into lines for line-by-line processing
  const lines = props.content.split('\n')

  const highlightedLines = lines.map(line => {
    // Escape HTML first to prevent XSS
    let escapedLine = line
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')

    // Skip empty lines
    if (!escapedLine.trim()) return escapedLine

    // Handle comments first (entire line or end of line)
    if (escapedLine.trim().startsWith('#')) {
      return `<span style="color: #6b7280; font-style: italic;">${escapedLine}</span>`
    }

    // Handle YAML key-value pairs
    const keyValueMatch = escapedLine.match(/^(\s*)([\w-]+)(\s*)(:)(.*)$/)
    if (keyValueMatch) {
      const [, indent, key, spacing, colon, value] = keyValueMatch
      let highlightedValue = value

      // Highlight comments in values
      if (value.includes('#')) {
        const commentIndex = value.indexOf('#')
        const valueBeforeComment = value.substring(0, commentIndex)
        const comment = value.substring(commentIndex)
        highlightedValue = `<span style="color: #059669;">${valueBeforeComment}</span><span style="color: #6b7280; font-style: italic;">${comment}</span>`
      } else if (value.trim()) {
        highlightedValue = `<span style="color: #059669;">${value}</span>`
      }

      return `${indent}<span style="color: #2563eb; font-weight: 500;">${key}</span>${spacing}<span style="color: #6b7280;">${colon}</span>${highlightedValue}`
    }

    // Handle list items - preserve the dash, don't replace with bullet
    const listMatch = escapedLine.match(/^(\s*-\s*)(.*)$/)
    if (listMatch) {
      const [, listPrefix, content] = listMatch
      let highlightedContent = content

      // Highlight comments in list items
      if (content.includes('#')) {
        const commentIndex = content.indexOf('#')
        const contentBeforeComment = content.substring(0, commentIndex)
        const comment = content.substring(commentIndex)
        highlightedContent = `${contentBeforeComment}<span style="color: #6b7280; font-style: italic;">${comment}</span>`
      }

      // Keep the original dash, just highlight it
      return `<span style="color: #ea580c;">${listPrefix}</span>${highlightedContent}`
    }

    // Handle standalone comments
    const commentMatch = escapedLine.match(/^(\s*)(#.*)$/)
    if (commentMatch) {
      const [, indent, comment] = commentMatch
      return `${indent}<span style="color: #6b7280; font-style: italic;">${comment}</span>`
    }

    return escapedLine
  })

  return highlightedLines.join('\n')
})

const toggleLineNumbers = () => {
  showLineNumbers.value = !showLineNumbers.value
}

const toggleWordWrap = () => {
  wordWrap.value = !wordWrap.value
}

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(props.content)
    toast.success('YAML content copied to clipboard')
  } catch (error) {
    toast.error('Failed to copy to clipboard')
  }
}

const downloadFile = () => {
  const blob = new Blob([props.content], { type: 'text/yaml' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = props.filename.endsWith('.yaml') ? props.filename : `${props.filename}.yaml`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>

<style scoped>
.yaml-viewer {
  width: 100%;
}

/* Word wrap styles */
pre.whitespace-pre-wrap {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

pre.whitespace-pre {
  white-space: pre;
  overflow-x: auto;
}

/* Custom scrollbar for code container */
.overflow-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.overflow-auto::-webkit-scrollbar-track {
  background-color: #f3f4f6;
}

.dark .overflow-auto::-webkit-scrollbar-track {
  background-color: #1f2937;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 4px;
}

.dark .overflow-auto::-webkit-scrollbar-thumb {
  background-color: #4b5563;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

.dark .overflow-auto::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}
</style>
