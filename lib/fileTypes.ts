export interface FileTypeConfig {
  type: 'yaml' | 'markdown' | 'code' | 'text'
  language?: string
  icon: string
  displayName: string
  mimeType: string
  extensions: string[]
  supportsLineNumbers: boolean
  supportsWordWrap: boolean
  defaultHighlighting: boolean
}

export interface FileInfo {
  config: FileTypeConfig
  filename: string
  extension: string
  basename: string
}

// File type configurations
const FILE_TYPE_CONFIGS: Record<string, FileTypeConfig> = {
  // YAML files
  yaml: {
    type: 'yaml',
    language: 'yaml',
    icon: 'FileText',
    displayName: 'YAML',
    mimeType: 'text/yaml',
    extensions: ['.yaml', '.yml'],
    supportsLineNumbers: true,
    supportsWordWrap: true,
    defaultHighlighting: true
  },

  // Markdown files
  markdown: {
    type: 'markdown',
    language: 'markdown',
    icon: 'FileText',
    displayName: 'Markdown',
    mimeType: 'text/markdown',
    extensions: ['.md', '.markdown', '.mdown', '.mkd'],
    supportsLineNumbers: false,
    supportsWordWrap: true,
    defaultHighlighting: false
  },

  // Python files
  python: {
    type: 'code',
    language: 'python',
    icon: 'Code',
    displayName: 'Python',
    mimeType: 'text/x-python',
    extensions: ['.py', '.pyw', '.pyi'],
    supportsLineNumbers: true,
    supportsWordWrap: true,
    defaultHighlighting: true
  },

  // JavaScript/TypeScript files
  javascript: {
    type: 'code',
    language: 'javascript',
    icon: 'Code',
    displayName: 'JavaScript',
    mimeType: 'text/javascript',
    extensions: ['.js', '.mjs', '.cjs'],
    supportsLineNumbers: true,
    supportsWordWrap: true,
    defaultHighlighting: true
  },

  typescript: {
    type: 'code',
    language: 'typescript',
    icon: 'Code',
    displayName: 'TypeScript',
    mimeType: 'text/typescript',
    extensions: ['.ts', '.tsx'],
    supportsLineNumbers: true,
    supportsWordWrap: true,
    defaultHighlighting: true
  },

  // Vue files
  vue: {
    type: 'code',
    language: 'vue',
    icon: 'Code',
    displayName: 'Vue',
    mimeType: 'text/x-vue',
    extensions: ['.vue'],
    supportsLineNumbers: true,
    supportsWordWrap: true,
    defaultHighlighting: true
  },

  // JSON files
  json: {
    type: 'code',
    language: 'json',
    icon: 'Braces',
    displayName: 'JSON',
    mimeType: 'application/json',
    extensions: ['.json', '.jsonc'],
    supportsLineNumbers: true,
    supportsWordWrap: true,
    defaultHighlighting: true
  },

  // Shell scripts
  shell: {
    type: 'code',
    language: 'bash',
    icon: 'Terminal',
    displayName: 'Shell Script',
    mimeType: 'text/x-shellscript',
    extensions: ['.sh', '.bash', '.zsh', '.fish'],
    supportsLineNumbers: true,
    supportsWordWrap: true,
    defaultHighlighting: true
  },

  // SQL files
  sql: {
    type: 'code',
    language: 'sql',
    icon: 'Database',
    displayName: 'SQL',
    mimeType: 'text/x-sql',
    extensions: ['.sql'],
    supportsLineNumbers: true,
    supportsWordWrap: true,
    defaultHighlighting: true
  },

  // Text files (fallback)
  text: {
    type: 'text',
    icon: 'FileText',
    displayName: 'Text',
    mimeType: 'text/plain',
    extensions: ['.txt', '.log', '.cfg', '.conf', '.ini'],
    supportsLineNumbers: true,
    supportsWordWrap: true,
    defaultHighlighting: false
  }
}

/**
 * Detect file type based on filename/extension
 */
export function detectFileType(filename: string): FileInfo {
  const extension = getFileExtension(filename)
  const basename = getBasename(filename)

  // Find matching file type config
  for (const [key, config] of Object.entries(FILE_TYPE_CONFIGS)) {
    if (config.extensions.includes(extension)) {
      return {
        config,
        filename,
        extension,
        basename
      }
    }
  }

  // Fallback to text type
  return {
    config: FILE_TYPE_CONFIGS.text,
    filename,
    extension,
    basename
  }
}

/**
 * Get file extension (including the dot)
 */
export function getFileExtension(filename: string): string {
  const lastDotIndex = filename.lastIndexOf('.')
  return lastDotIndex === -1 ? '' : filename.substring(lastDotIndex).toLowerCase()
}

/**
 * Get basename without extension
 */
export function getBasename(filename: string): string {
  const lastSlashIndex = filename.lastIndexOf('/')
  const nameWithoutPath = lastSlashIndex === -1 ? filename : filename.substring(lastSlashIndex + 1)
  const lastDotIndex = nameWithoutPath.lastIndexOf('.')
  return lastDotIndex === -1 ? nameWithoutPath : nameWithoutPath.substring(0, lastDotIndex)
}

/**
 * Check if file type supports syntax highlighting
 */
export function supportsSyntaxHighlighting(fileInfo: FileInfo): boolean {
  return fileInfo.config.type === 'code' || fileInfo.config.type === 'yaml'
}

/**
 * Get appropriate download filename with extension
 */
export function getDownloadFilename(fileInfo: FileInfo): string {
  if (fileInfo.filename.includes('.')) {
    return fileInfo.filename
  }
  
  // Add appropriate extension if missing
  const primaryExtension = fileInfo.config.extensions[0]
  return `${fileInfo.filename}${primaryExtension}`
}

/**
 * Get all supported file extensions
 */
export function getSupportedExtensions(): string[] {
  const extensions = new Set<string>()
  
  Object.values(FILE_TYPE_CONFIGS).forEach(config => {
    config.extensions.forEach(ext => extensions.add(ext))
  })
  
  return Array.from(extensions).sort()
}

/**
 * Check if a file extension is supported
 */
export function isFileSupported(filename: string): boolean {
  const extension = getFileExtension(filename)
  return getSupportedExtensions().includes(extension)
}
