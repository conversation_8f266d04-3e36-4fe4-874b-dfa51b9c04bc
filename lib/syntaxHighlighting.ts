import { createHighlighter, type Highlighter, type BundledLanguage, type BundledTheme } from 'shiki'
import type { FileInfo } from './fileTypes'

// Shiki highlighter instance (singleton)
let highlighter: Highlighter | null = null

// Supported languages mapping
const LANGUAGE_MAP: Record<string, BundledLanguage> = {
  'yaml': 'yaml',
  'python': 'python',
  'javascript': 'javascript',
  'typescript': 'typescript',
  'vue': 'vue',
  'json': 'json',
  'bash': 'bash',
  'shell': 'bash',
  'sql': 'sql',
  'markdown': 'markdown',
  'html': 'html',
  'css': 'css',
  'scss': 'scss',
  'less': 'less'
}

// Theme configuration
const THEMES: Record<string, BundledTheme> = {
  light: 'github-light',
  dark: 'github-dark'
}

/**
 * Initialize the Shiki highlighter
 */
async function initHighlighter(): Promise<Highlighter> {
  if (highlighter) {
    return highlighter
  }

  try {
    highlighter = await createHighlighter({
      themes: Object.values(THEMES),
      langs: Object.values(LANGUAGE_MAP)
    })
    return highlighter
  } catch (error) {
    console.error('Failed to initialize Shiki highlighter:', error)
    throw new Error('Syntax highlighting not available')
  }
}

/**
 * Highlight code using Shiki
 */
export async function highlightCode(
  code: string,
  fileInfo: FileInfo,
  theme: 'light' | 'dark' = 'light'
): Promise<string> {
  try {
    const shiki = await initHighlighter()

    // Get the language for Shiki
    const language = getShikiLanguage(fileInfo)

    if (!language) {
      // Return escaped HTML for unsupported languages
      return escapeHtml(code)
    }

    // Highlight the code
    const highlighted = shiki.codeToHtml(code, {
      lang: language,
      theme: THEMES[theme]
    })

    return highlighted
  } catch (error) {
    console.error('Error highlighting code:', error)
    // Fallback to escaped HTML
    return `<pre><code>${escapeHtml(code)}</code></pre>`
  }
}

/**
 * Get Shiki language from file info
 */
function getShikiLanguage(fileInfo: FileInfo): BundledLanguage | null {
  const language = fileInfo.config.language
  if (!language) return null

  return LANGUAGE_MAP[language] || null
}

/**
 * Simple YAML syntax highlighting (fallback for custom implementation)
 */
export function highlightYaml(content: string): string {
  if (!content) return ''

  const lines = content.split('\n')

  const highlightedLines = lines.map(line => {
    // Escape HTML first to prevent XSS
    let escapedLine = escapeHtml(line)

    // Skip empty lines
    if (!escapedLine.trim()) return escapedLine

    // Handle comments first (entire line or end of line)
    if (escapedLine.trim().startsWith('#')) {
      return `<span class="text-gray-500 italic">${escapedLine}</span>`
    }

    // Handle YAML key-value pairs
    const keyValueMatch = escapedLine.match(/^(\s*)([\w-]+)(\s*)(:)(.*)$/)
    if (keyValueMatch) {
      const [, indent, key, spacing, colon, value] = keyValueMatch
      let highlightedValue = value

      // Highlight comments in values
      if (value.includes('#')) {
        const commentIndex = value.indexOf('#')
        const valueBeforeComment = value.substring(0, commentIndex)
        const comment = value.substring(commentIndex)
        highlightedValue = `<span class="text-green-600">${valueBeforeComment}</span><span class="text-gray-500 italic">${comment}</span>`
      } else if (value.trim()) {
        highlightedValue = `<span class="text-green-600">${value}</span>`
      }

      return `${indent}<span class="text-blue-600 font-medium">${key}</span>${spacing}<span class="text-gray-500">${colon}</span>${highlightedValue}`
    }

    // Handle list items
    const listMatch = escapedLine.match(/^(\s*-\s*)(.*)$/)
    if (listMatch) {
      const [, listPrefix, content] = listMatch
      let highlightedContent = content

      // Highlight comments in list items
      if (content.includes('#')) {
        const commentIndex = content.indexOf('#')
        const contentBeforeComment = content.substring(0, commentIndex)
        const comment = content.substring(commentIndex)
        highlightedContent = `${contentBeforeComment}<span class="text-gray-500 italic">${comment}</span>`
      }

      return `<span class="text-orange-600">${listPrefix}</span>${highlightedContent}`
    }

    // Handle standalone comments
    const commentMatch = escapedLine.match(/^(\s*)(#.*)$/)
    if (commentMatch) {
      const [, indent, comment] = commentMatch
      return `${indent}<span class="text-gray-500 italic">${comment}</span>`
    }

    return escapedLine
  })

  return highlightedLines.join('\n')
}

/**
 * Escape HTML characters (works in both browser and server environments)
 */
function escapeHtml(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

/**
 * Server-side HTML escaping (for Node.js environment)
 */
export function escapeHtmlServer(text: string): string {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

/**
 * Check if syntax highlighting is available for a file type
 */
export function canHighlight(fileInfo: FileInfo): boolean {
  if (fileInfo.config.type === 'yaml') return true
  if (fileInfo.config.type === 'code' && fileInfo.config.language) {
    return fileInfo.config.language in LANGUAGE_MAP
  }
  return false
}

/**
 * Get available themes
 */
export function getAvailableThemes(): string[] {
  return Object.keys(THEMES)
}

/**
 * Preload highlighter (useful for better performance)
 */
export async function preloadHighlighter(): Promise<void> {
  try {
    await initHighlighter()
  } catch (error) {
    console.warn('Failed to preload syntax highlighter:', error)
  }
}
