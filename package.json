{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@nuxt/content": "3.6.1", "@nuxt/fonts": "0.11.4", "@nuxt/test-utils": "3.19.1", "@nuxtjs/color-mode": "^3.5.2", "@tailwindcss/vite": "^4.1.10", "@vueuse/core": "^13.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fuse.js": "^7.1.0", "lucide-vue-next": "^0.522.0", "nuxt": "^3.17.5", "reka-ui": "^2.3.1", "shadcn-nuxt": "2.2.0", "shiki": "^3.7.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "vue": "^3.5.16", "vue-router": "^4.5.1", "vue-sonner": "^2.0.0"}}