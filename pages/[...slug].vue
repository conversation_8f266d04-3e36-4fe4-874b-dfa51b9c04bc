<template>
  <div>
    <div v-if="pending" class="flex items-center justify-center py-12">
      <div class="text-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
        <p class="text-muted-foreground">Loading...</p>
      </div>
    </div>

    <div v-else-if="error" class="text-center py-12">
      <div class="max-w-md mx-auto">
        <div class="p-6 border border-destructive/20 rounded-lg bg-destructive/5">
          <AlertCircle class="h-12 w-12 text-destructive mx-auto mb-4" />
          <h2 class="text-xl font-semibold mb-2">File Not Found</h2>
          <p class="text-muted-foreground mb-4">
            The requested file could not be found.
          </p>
          <Button as="NuxtLink" to="/" variant="outline">
            Go Home
          </Button>
        </div>
      </div>
    </div>

    <FileViewer v-else-if="fileContent" :content="fileContent.content" :title="fileContent.title"
      :description="fileContent.description" :filename="fileContent.filename" :file-info="fileContent.fileInfo"
      :breadcrumbs="breadcrumbs" :metadata="fileContent.metadata" />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { AlertCircle } from 'lucide-vue-next'
import { Button } from '~/components/ui/button'

const route = useRoute()

// Get the slug from the route
const slug = computed(() => {
  const slugArray = Array.isArray(route.params.slug) ? route.params.slug : [route.params.slug]
  return slugArray.join('/')
})

// Load YAML content directly from files
const { data: yamlContent, pending, error } = await useLazyAsyncData(
  `yaml-${slug.value}`,
  async () => {
    try {
      // Get the raw YAML content directly
      const response = await $fetch(`/api/content-raw?path=${slug.value}`)

      if (!response || !response.content) {
        throw createError({
          statusCode: 404,
          statusMessage: 'YAML file not found'
        })
      }

      // Extract title from the first comment line if available
      const lines = response.content.split('\n')
      let title = slug.value.split('/').pop()?.replace(/[-_]/g, ' ') || 'Untitled'
      let description = ''

      // Look for title in first few comment lines
      for (let i = 0; i < Math.min(5, lines.length); i++) {
        const line = lines[i].trim()
        if (line.startsWith('#')) {
          const commentText = line.substring(1).trim()
          if (i === 0 && commentText) {
            title = commentText
          } else if (i === 1 && commentText) {
            description = commentText
          }
        }
      }

      return {
        title: title.charAt(0).toUpperCase() + title.slice(1),
        description,
        filename: `${slug.value.split('/').pop()}.yaml`,
        content: response.content,
        metadata: {
          lastModified: new Date().toISOString(),
          tags: []
        }
      }
    } catch (err) {
      throw createError({
        statusCode: 404,
        statusMessage: 'YAML file not found'
      })
    }
  }
)



// Generate breadcrumbs
const breadcrumbs = computed(() => {
  const parts = slug.value.split('/')
  const crumbs = []

  for (let i = 0; i < parts.length; i++) {
    const path = '/' + parts.slice(0, i + 1).join('/')
    const title = parts[i].charAt(0).toUpperCase() + parts[i].slice(1)

    crumbs.push({
      title,
      path: i === parts.length - 1 ? undefined : path // Don't link the current page
    })
  }

  return crumbs
})

// Set page meta
useHead(() => ({
  title: yamlContent.value ? `${yamlContent.value.title} - YAML Viewer` : 'Loading...',
  meta: [
    {
      name: 'description',
      content: yamlContent.value?.description || 'YAML documentation file'
    }
  ]
}))
</script>
