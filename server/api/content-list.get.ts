import { readdir, stat } from 'fs/promises'
import { join } from 'path'
import { detectFileType, isFileSupported } from '~/lib/fileTypes'

interface ContentFile {
  _path: string
  title: string
  description?: string
  _file?: string
  _extension?: string
  fileType?: {
    type: string
    displayName: string
    language?: string
    icon: string
  }
}

async function scanContentDirectory(dir: string, basePath: string = ''): Promise<ContentFile[]> {
  const files: ContentFile[] = []
  const contentDir = join(process.cwd(), 'content', dir)

  try {
    const entries = await readdir(contentDir)

    for (const entry of entries) {
      const fullPath = join(contentDir, entry)
      const stats = await stat(fullPath)
      const relativePath = basePath ? `${basePath}/${entry}` : entry

      if (stats.isDirectory()) {
        // Recursively scan subdirectories
        const subFiles = await scanContentDirectory(join(dir, entry), relativePath)
        files.push(...subFiles)
      } else if (isFileSupported(entry)) {
        // This is a supported file type
        const fileInfo = detectFileType(entry)
        const nameWithoutExt = fileInfo.basename
        const pathWithoutExt = basePath ? `/${basePath}/${nameWithoutExt}` : `/${nameWithoutExt}`

        files.push({
          _path: pathWithoutExt,
          title: nameWithoutExt.charAt(0).toUpperCase() + nameWithoutExt.slice(1).replace(/[-_]/g, ' '),
          description: `${fileInfo.config.displayName} file`,
          _file: entry,
          _extension: fileInfo.extension.substring(1), // Remove the dot
          fileType: {
            type: fileInfo.config.type,
            displayName: fileInfo.config.displayName,
            language: fileInfo.config.language,
            icon: fileInfo.config.icon
          }
        })
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dir}:`, error)
  }

  return files
}

export default defineEventHandler(async () => {
  try {
    const files = await scanContentDirectory('')
    return files
  } catch (error) {
    console.error('Error loading content files:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to load content files'
    })
  }
})
