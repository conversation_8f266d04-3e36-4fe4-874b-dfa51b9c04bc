import { readFile, stat } from 'fs/promises'
import { join, extname } from 'path'
import { detectFileType, getSupportedExtensions } from '~/lib/fileTypes'

export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const path = query.path as string

  if (!path) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Path parameter is required'
    })
  }

  try {
    // Try to find the file with various supported extensions
    let filePath: string | null = null
    let actualFilename: string | null = null

    // First, check if the path already includes an extension
    const pathWithExtension = join(process.cwd(), 'content', path)
    if (extname(path)) {
      try {
        await stat(pathWithExtension)
        filePath = pathWithExtension
        actualFilename = path.split('/').pop() || path
      } catch {
        // File doesn't exist with provided extension
      }
    }

    // If no file found yet, try with supported extensions
    if (!filePath) {
      const supportedExtensions = getSupportedExtensions()

      for (const ext of supportedExtensions) {
        const testPath = join(process.cwd(), 'content', `${path}${ext}`)
        try {
          await stat(testPath)
          filePath = testPath
          actualFilename = `${path.split('/').pop() || path}${ext}`
          break
        } catch {
          // Continue to next extension
        }
      }
    }

    if (!filePath || !actualFilename) {
      throw createError({
        statusCode: 404,
        statusMessage: 'File not found'
      })
    }

    // Read the raw file content
    const content = await readFile(filePath, 'utf-8')

    // Get file stats
    const stats = await stat(filePath)

    // Detect file type
    const fileInfo = detectFileType(actualFilename)

    return {
      content,
      path,
      filename: actualFilename,
      fileInfo: {
        type: fileInfo.config.type,
        language: fileInfo.config.language,
        displayName: fileInfo.config.displayName,
        mimeType: fileInfo.config.mimeType,
        extension: fileInfo.extension,
        basename: fileInfo.basename,
        supportsLineNumbers: fileInfo.config.supportsLineNumbers,
        supportsWordWrap: fileInfo.config.supportsWordWrap,
        defaultHighlighting: fileInfo.config.defaultHighlighting
      },
      metadata: {
        size: stats.size,
        lastModified: stats.mtime.toISOString(),
        created: stats.birthtime.toISOString()
      }
    }
  } catch (error: any) {
    if (error?.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 404,
      statusMessage: 'File not found'
    })
  }
})
